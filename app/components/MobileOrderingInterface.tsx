"use client";

import React, { useState, useReducer, use<PERSON><PERSON>back, useMemo, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card, CardContent } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useToast } from "@/components/ui/use-toast";
import { cn } from "@/lib/utils";
import {
  Search,
  Utensils,
  Plus,
  Minus,
  X,
  ShoppingCart,
  Check,
  Pizza,
  StickyNote,
  Package,
  AlertCircle
} from "lucide-react";

// Hooks and utilities
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { useMenuV4 } from '@/lib/hooks/use-menu-v4';
import { useTableDB } from '@/lib/hooks/useTableDB';
import { useOrderV4 } from '@/lib/hooks/use-order-v4';
import { useStaffMenuV4 } from '@/lib/hooks/useStaffMenuV4';
import { useSupplements } from '@/lib/hooks/useSupplements';
import { runUniversalSeedV4 } from '@/lib/seed-data/universal-seed';

// Types
import { MenuItem } from '@/lib/db/v4/schemas/menu-schema';
import { OrderType } from '@/lib/types/order-types';
import { NewOrder } from '@/lib/db/v4/schemas/order-schema';

// Order types and interfaces
interface OrderAddon {
  id: string;
  name: string;
  price: number;
}

interface PizzaQuarter {
  id: string;
  name: string;
  price: number;
}

interface OrderItem {
  id: string;
  menuItemId: string;
  name: string;
  size: string;
  price: number;
  quantity: number;
  addons: OrderAddon[];
  notes: string;
  categoryId: string;
  compositeType?: 'pizza_quarters';
  quarters?: PizzaQuarter[];
}

interface OrderState {
  items: OrderItem[];
  total: number;
  orderType: OrderType;
  tableId: string;
  notes: string;
}

interface UiState {
  selectedCategory: string;
  searchQuery: string;
  selectedItemForAddons: string | null;
  selectedItemSizes: {[key: string]: string};
  selectedAddons: {[key: string]: Set<string>};
  itemNotes: {[key: string]: string};
  lastAddedItem: string | null;
  showTableSelection: boolean;
  showOrderSummary: boolean;
  showCustomization: boolean;
}

// Initial states
const initialOrderState: OrderState = {
  items: [],
  total: 0,
  orderType: 'dine-in', // Fixed to dine-in only
  tableId: '',
  notes: ''
};

const initialUiState: UiState = {
  selectedCategory: '',
  searchQuery: '',
  selectedItemForAddons: null,
  selectedItemSizes: {},
  selectedAddons: {},
  itemNotes: {},
  lastAddedItem: null,
  showTableSelection: false,
  showOrderSummary: false,
  showCustomization: false
};

// Utility functions
const getItemColor = (categoryId: string, itemId: string, categories: any[]): string => {
  const category = categories?.find(cat => cat.id === categoryId);
  return category?.color || '#f3f4f6';
};

const getLightColor = (color: string): string => {
  // Simple function to lighten a color
  return color + '20'; // Add transparency
};

// Order reducer actions
type OrderAction =
  | { type: 'INITIALIZE_ORDER' }
  | { type: 'ADD_ITEM', payload: { item: MenuItem, size: string, addons: OrderAddon[], notes: string, categoryId: string } }
  | { type: 'ADD_CUSTOM_PIZZA', payload: { quarters: PizzaQuarter[], size: string, notes: string, categoryId: string, pricingMethod: 'max' | 'average' } }
  | { type: 'UPDATE_ITEM', payload: { itemId: string, updates: Partial<OrderItem> } }
  | { type: 'INCREMENT_QUANTITY', payload: { itemId: string } }
  | { type: 'DECREMENT_QUANTITY', payload: { itemId: string } }
  | { type: 'REMOVE_ITEM', payload: { itemId: string } }
  | { type: 'SET_TABLE', payload: { tableId: string } }
  | { type: 'SET_NOTES', payload: { notes: string } };

// Calculate total helper
const calculateTotal = (items: OrderItem[]): number => {
  return items.reduce((total, item) => {
    const itemTotal = item.price * item.quantity;
    const addonsTotal = item.addons.reduce((sum, addon) => sum + addon.price, 0) * item.quantity;
    return total + itemTotal + addonsTotal;
  }, 0);
};

// Order reducer
const orderReducer = (state: OrderState, action: OrderAction): OrderState => {
  switch (action.type) {
    case 'INITIALIZE_ORDER':
      return initialOrderState;
      
    case 'ADD_ITEM': {
      const { item, size, addons, notes, categoryId } = action.payload;
      const price = item.prices[size] || Object.values(item.prices)[0] || 0;
      const uniqueId = `order_item_${Date.now()}_${Math.floor(Math.random() * 10000)}`;
      
      const newItem: OrderItem = {
        id: uniqueId,
        menuItemId: item.id,
        name: item.name,
        size: size,
        price: price,
        quantity: 1,
        addons: addons,
        notes: notes,
        categoryId: categoryId
      };
      
      const updatedItems = [...state.items, newItem];
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }
      
    case 'ADD_CUSTOM_PIZZA': {
      const { quarters, size, notes, categoryId, pricingMethod } = action.payload;
      const uniqueId = `custom_pizza_${Date.now()}_${Math.floor(Math.random() * 10000)}`;
      
      let price = 0;
      if (pricingMethod === 'max') {
        price = Math.max(...quarters.map(q => q.price));
      } else {
        price = quarters.reduce((sum, q) => sum + q.price, 0) / quarters.length;
      }
      
      const newItem: OrderItem = {
        id: uniqueId,
        menuItemId: 'custom_pizza',
        name: `Pizza Personnalisée (${quarters.map(q => q.name).join(', ')})`,
        size: size,
        price: price,
        quantity: 1,
        addons: [],
        notes: notes,
        categoryId: categoryId,
        compositeType: 'pizza_quarters',
        quarters: quarters
      };
      
      const updatedItems = [...state.items, newItem];
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }
      
    case 'UPDATE_ITEM': {
      const { itemId, updates } = action.payload;
      const updatedItems = state.items.map(item => 
        item.id === itemId ? { ...item, ...updates } : item
      );
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }
      
    case 'INCREMENT_QUANTITY': {
      const { itemId } = action.payload;
      const updatedItems = state.items.map(item => 
        item.id === itemId ? { ...item, quantity: item.quantity + 1 } : item
      );
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }
      
    case 'DECREMENT_QUANTITY': {
      const { itemId } = action.payload;
      const targetItem = state.items.find(item => item.id === itemId);
      
      if (!targetItem || targetItem.quantity <= 1) {
        const updatedItems = state.items.filter(item => item.id !== itemId);
        return {
          ...state,
          items: updatedItems,
          total: calculateTotal(updatedItems)
        };
      }
      
      const updatedItems = state.items.map(item => 
        item.id === itemId ? { ...item, quantity: item.quantity - 1 } : item
      );
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }
      
    case 'REMOVE_ITEM': {
      const { itemId } = action.payload;
      const updatedItems = state.items.filter(item => item.id !== itemId);
      return {
        ...state,
        items: updatedItems,
        total: calculateTotal(updatedItems)
      };
    }
      
    case 'SET_TABLE': {
      return {
        ...state,
        tableId: action.payload.tableId
      };
    }
      
    case 'SET_NOTES': {
      return {
        ...state,
        notes: action.payload.notes
      };
    }
      
    default:
      return state;
  }
};

// Main component
const MobileOrderingInterface: React.FC = () => {
  // Hooks
  const { isAuthenticated, user } = useAuth();
  const { categories, isLoading: menuLoading, error: menuError, isReady: menuReady } = useMenuV4();
  const { tables, isLoading: tablesLoading, error: tablesError, isReady: tablesReady } = useTableDB();
  const { createOrder } = useOrderV4();
  const { toast } = useToast();

  // State
  const [orderState, dispatch] = useReducer(orderReducer, initialOrderState);
  const [uiState, setUiState] = useState<UiState>(initialUiState);
  const [isPlacingOrder, setIsPlacingOrder] = useState(false);
  const [isSeeding, setIsSeeding] = useState(false);

  // Debug logging for categories
  useEffect(() => {
    if (categories && categories.length > 0) {
      console.log('📂 Categories loaded:', categories.map(cat => ({
        id: cat.id,
        name: cat.name,
        itemsCount: cat.items?.length || 0,
        supplementsCount: cat.supplements?.length || 0,
        hasSupplementConfig: !!cat.supplementConfig,
        supplementConfig: cat.supplementConfig
      })));
    }
  }, [categories]);

  // Show table selection if no table is selected
  useEffect(() => {
    if (!orderState.tableId && tablesReady) {
      setUiState(prev => ({ ...prev, showTableSelection: true }));
    }
  }, [orderState.tableId, tablesReady]);

  // Set initial category when menu loads
  useEffect(() => {
    if (menuReady && categories && categories.length > 0 && !uiState.selectedCategory) {
      setUiState(prev => ({ ...prev, selectedCategory: categories[0].id }));
    }
  }, [menuReady, categories, uiState.selectedCategory]);

  // Helper functions
  const getAddonKey = useCallback((itemId: string, size: string) => `${itemId}-${size}`, []);
  const getItemNoteKey = useCallback((itemId: string, size: string) => `${itemId}-${size}-note`, []);

  // Seed function for testing
  const handleSeed = async () => {
    setIsSeeding(true);
    try {
      await runUniversalSeedV4();
      toast({ title: "Seed réussi!", description: "Données de test créées avec succès" });
      // Refresh the page to load new data
      window.location.reload();
    } catch (error) {
      console.error('Seed error:', error);
      toast({ title: "Erreur", description: "Échec du seed", variant: "destructive" });
    } finally {
      setIsSeeding(false);
    }
  };

  // Handler functions
  const handleIncrement = useCallback((itemId: string) => {
    dispatch({ type: 'INCREMENT_QUANTITY', payload: { itemId } });
  }, []);

  const handleDecrement = useCallback((itemId: string) => {
    dispatch({ type: 'DECREMENT_QUANTITY', payload: { itemId } });
  }, []);

  const handleRemoveItem = useCallback((itemId: string) => {
    dispatch({ type: 'REMOVE_ITEM', payload: { itemId } });
  }, []);

  const handleItemEdit = useCallback((item: OrderItem) => {
    // Set up customization panel for editing
    setUiState(prev => ({
      ...prev,
      selectedItemForAddons: `${item.menuItemId}-${item.size}`,
      selectedItemSizes: { ...prev.selectedItemSizes, [item.menuItemId]: item.size },
      selectedAddons: {
        ...prev.selectedAddons,
        [getAddonKey(item.menuItemId, item.size)]: new Set(item.addons.map(a => a.id))
      },
      itemNotes: {
        ...prev.itemNotes,
        [getItemNoteKey(item.menuItemId, item.size)]: item.notes
      }
    }));
  }, [getAddonKey, getItemNoteKey]);

  const handleItemSelect = useCallback((itemId: string, size: string) => {
    setUiState(prev => ({
      ...prev,
      selectedItemForAddons: `${itemId}-${size}`,
      selectedItemSizes: { ...prev.selectedItemSizes, [itemId]: size }
    }));
  }, []);

  const handleAddItem = useCallback(async (item: MenuItem, size: string) => {
    // Find category for this item
    let categoryId = uiState.selectedCategory;
    for (const category of categories || []) {
      const foundItem = category.items?.find((menuItem: MenuItem) => menuItem.id === item.id);
      if (foundItem) {
        categoryId = category.id;
        break;
      }
    }

    const selectedAddons = uiState.selectedAddons[getAddonKey(item.id, size)] || new Set();
    const itemNotes = uiState.itemNotes[getItemNoteKey(item.id, size)] || '';

    // For now, create empty addons array - will be populated by customization panel
    const validAddonObjects: OrderAddon[] = [];

    dispatch({
      type: 'ADD_ITEM',
      payload: {
        item,
        size,
        addons: validAddonObjects,
        notes: itemNotes,
        categoryId
      }
    });

    // Set visual feedback
    const signature = `${item.id}-${size}`;
    setUiState(prev => ({ ...prev, lastAddedItem: signature }));

    // Check if item has supplements or is pizza - then show customization
    const category = categories?.find(cat => cat.id === categoryId);
    const hasSupplements = category && category.items?.some((menuItem: MenuItem) => menuItem.id === item.id);
    const isPizza = category?.name.toLowerCase().includes('pizza');

    if (hasSupplements || isPizza) {
      setUiState(prev => ({
        ...prev,
        selectedItemForAddons: `${item.id}-${size}`,
        selectedItemSizes: { ...prev.selectedItemSizes, [item.id]: size },
        showCustomization: true
      }));
    }

    toast({
      title: "Article ajouté",
      description: `${item.name} (${size}) ajouté à la commande`,
    });
  }, [uiState.selectedCategory, uiState.selectedAddons, uiState.itemNotes, categories, getAddonKey, getItemNoteKey, dispatch, toast]);

  const handlePlaceOrder = useCallback(async () => {
    if (orderState.items.length === 0) {
      toast({
        title: "Commande vide",
        description: "Ajoutez des articles avant de passer la commande",
        variant: "destructive"
      });
      return;
    }

    if (!orderState.tableId) {
      toast({
        title: "Table non sélectionnée",
        description: "Sélectionnez une table pour continuer",
        variant: "destructive"
      });
      return;
    }

    setIsPlacingOrder(true);

    try {
      const newOrder: NewOrder = {
        tableId: orderState.tableId,
        orderType: 'dine-in',
        status: 'pending',
        items: orderState.items.map(item => ({ ...item, notes: item.notes || '' })),
        total: orderState.total,
        notes: orderState.notes,
        paymentStatus: 'unpaid',
        createdBy: user?.name || (user as any)?.username || 'unknown',
        createdByName: user?.name || 'Personnel Inconnu'
      };

      await createOrder(newOrder);

      toast({
        title: "✅ Commande créée",
        description: `Commande pour la table ${tables?.find(t => t.id === orderState.tableId)?.name} créée avec succès`,
      });

      // Reset order
      dispatch({ type: 'INITIALIZE_ORDER' });
      setUiState(initialUiState);

    } catch (error) {
      console.error('Error creating order:', error);
      toast({
        title: "Erreur",
        description: "Impossible de créer la commande. Veuillez réessayer.",
        variant: "destructive"
      });
    } finally {
      setIsPlacingOrder(false);
    }
  }, [orderState, user, createOrder, tables, toast]);

  const handleCustomizationConfirm = useCallback(async () => {
    if (!uiState.selectedItemForAddons) return;

    const [itemId, sizeFromKey] = uiState.selectedItemForAddons.split('-');
    const selectedSize = uiState.selectedItemSizes[itemId] || sizeFromKey || 'default';

    // Find the most recent item in the order that matches this item and size
    const targetItem = [...orderState.items].reverse().find(item =>
      item.menuItemId === itemId && item.size === selectedSize
    );

    if (targetItem) {
      // Find category for supplements
      let categoryId = uiState.selectedCategory;
      let category = null;
      for (const cat of categories || []) {
        const foundItem = cat.items?.find((menuItem: MenuItem) => menuItem.id === itemId);
        if (foundItem) {
          categoryId = cat.id;
          category = cat;
          break;
        }
      }

      const selectedAddons = uiState.selectedAddons[getAddonKey(itemId, selectedSize)] || new Set();
      const itemNotes = uiState.itemNotes[getItemNoteKey(itemId, selectedSize)] || '';

      // Get supplements with proper names and prices
      const validAddonObjects: OrderAddon[] = [];
      if (category && category.supplements) {
        for (const addonId of Array.from(selectedAddons)) {
          const supplement = category.supplements.find((s: any) => s.id === addonId);
          if (supplement) {
            // Get price from category supplement config
            const supplementPrice = category.supplementConfig?.globalPricing?.[selectedSize] || 0;
            validAddonObjects.push({
              id: supplement.id,
              name: supplement.name,
              price: supplementPrice
            });
          }
        }
      }

      // Update the item
      dispatch({
        type: 'UPDATE_ITEM',
        payload: {
          itemId: targetItem.id,
          updates: {
            addons: validAddonObjects,
            notes: itemNotes
          }
        }
      });

      toast({
        title: "✅ Article personnalisé",
        description: `${validAddonObjects.length} supplément${validAddonObjects.length > 1 ? 's' : ''} ajouté${validAddonObjects.length > 1 ? 's' : ''}`,
      });
    }

    // Close the customization panel and clear selection
    setUiState(prev => ({
      ...prev,
      showCustomization: false,
      selectedItemForAddons: null
    }));
  }, [uiState.selectedItemForAddons, uiState.selectedItemSizes, uiState.selectedAddons, uiState.itemNotes, orderState.items, categories, getAddonKey, getItemNoteKey, dispatch, toast]);

  return (
    <div className="h-screen flex flex-col bg-gray-50 overflow-hidden">
      {/* Enhanced Mobile Header - Ultra Compact & Polished */}
      <div className="flex-shrink-0 bg-white border-b border-gray-200 shadow-sm">
        <div className="px-3 py-2 space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center shadow-sm">
                <Utensils className="h-4 w-4 text-white" />
              </div>
              <div>
                <h1 className="text-lg font-bold text-gray-900 leading-none">Commande</h1>
                {orderState.tableId && (
                  <p className="text-xs text-gray-500 leading-none mt-0.5">
                    Table {tables?.find(t => t.id === orderState.tableId)?.name}
                  </p>
                )}
              </div>
            </div>
            <div className="flex items-center gap-2">
              {/* Temporary Seed Button for Testing */}
              {process.env.NODE_ENV === 'development' && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSeed}
                  disabled={isSeeding}
                  className="text-xs h-7 px-2 rounded-lg"
                >
                  {isSeeding ? 'Seed...' : 'Seed'}
                </Button>
              )}
              {orderState.items.length > 0 && (
                <Badge variant="secondary" className="text-xs px-2 py-1 bg-blue-100 text-blue-700 border-blue-200">
                  {orderState.items.reduce((sum, item) => sum + item.quantity, 0)}
                </Badge>
              )}
            </div>
          </div>

          {/* Compact Search Bar */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Rechercher..."
              value={uiState.searchQuery}
              onChange={(e) => setUiState(prev => ({ ...prev, searchQuery: e.target.value }))}
              className="pl-10 h-9 text-sm bg-gray-50 border-gray-200 focus:bg-white focus:border-blue-300 rounded-xl"
            />
          </div>
        </div>
      </div>

      {/* Enhanced Category Navigation - Compact & Touch Optimized */}
      <div className="flex-shrink-0 bg-white border-b border-gray-100">
        <ScrollArea className="w-full">
          <div className="flex gap-1.5 px-3 py-2">
            {menuLoading ? (
              [...Array(4)].map((_, i) => (
                <div key={i} className="h-8 w-20 bg-gray-200 rounded-lg animate-pulse flex-shrink-0" />
              ))
            ) : (
              categories?.map((category) => {
                const isActive = uiState.selectedCategory === category.id;
                return (
                  <Button
                    key={category.id}
                    variant={isActive ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setUiState(prev => ({ ...prev, selectedCategory: category.id }))}
                    className={cn(
                      "flex-shrink-0 h-8 px-3 rounded-lg font-semibold transition-all text-xs active:scale-95",
                      isActive
                        ? "bg-blue-600 text-white shadow-md"
                        : "text-gray-600 hover:bg-blue-50 hover:text-blue-700"
                    )}
                  >
                    <span className="mr-1.5 text-sm">{category.emoji || '🍽️'}</span>
                    <span className="truncate max-w-16">{category.name}</span>
                  </Button>
                );
              })
            )}
          </div>
        </ScrollArea>
      </div>

      {/* Enhanced Main Content - Space-Efficient Mobile Layout */}
      <div className="flex-1 overflow-hidden relative">
        {/* Menu Items - Compact spacing */}
        <div className="h-full overflow-hidden">
          <ScrollArea className="h-full">
            <div className="p-3 pb-20">
              {menuLoading ? (
                <div className="space-y-3">
                  {[...Array(6)].map((_, i) => (
                    <div key={i} className="h-24 bg-gray-200 rounded-2xl animate-pulse" />
                  ))}
                </div>
              ) : (
                <MenuItemsGrid
                  categories={categories}
                  selectedCategory={uiState.selectedCategory}
                  searchQuery={uiState.searchQuery}
                  selectedItemForAddons={uiState.selectedItemForAddons}
                  selectedItemSizes={uiState.selectedItemSizes}
                  lastAddedItem={uiState.lastAddedItem}
                  onItemSelect={handleItemSelect}
                  onAddItem={handleAddItem}
                />
              )}
            </div>
          </ScrollArea>
        </div>

        {/* Enhanced Floating Order Summary Button - Compact & Polished */}
        {orderState.items.length > 0 && (
          <div className="absolute bottom-0 left-0 right-0 p-3 bg-gradient-to-t from-white via-white/95 to-transparent">
            <Button
              onClick={() => setUiState(prev => ({ ...prev, showOrderSummary: true }))}
              className="w-full h-12 text-base font-bold shadow-2xl rounded-2xl bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 active:scale-[0.98] transition-all"
            >
              <div className="flex items-center justify-between w-full">
                <div className="flex items-center">
                  <ShoppingCart className="h-4 w-4 mr-2" />
                  <span className="text-sm">
                    {orderState.items.reduce((sum, item) => sum + item.quantity, 0)} article{orderState.items.reduce((sum, item) => sum + item.quantity, 0) > 1 ? 's' : ''}
                  </span>
                </div>
                <span className="font-bold text-base">
                  {orderState.total.toFixed(0)} DA
                </span>
              </div>
            </Button>
          </div>
        )}
      </div>

      {/* Order Summary Modal - Fixed Accessibility */}
      <Dialog open={uiState.showOrderSummary} onOpenChange={(open) => setUiState(prev => ({ ...prev, showOrderSummary: open }))}>
        <DialogContent className="max-w-lg w-[95vw] h-[85vh] flex flex-col p-0 rounded-2xl" aria-describedby="order-summary-description">
          <DialogHeader className="p-4 border-b bg-white rounded-t-2xl">
            <DialogTitle className="text-xl font-bold text-gray-900">Votre commande</DialogTitle>
            <p id="order-summary-description" className="sr-only">
              Résumé de votre commande avec options de modification et finalisation
            </p>
          </DialogHeader>
          <MobileOrderSummary
            items={orderState.items}
            total={orderState.total}
            onIncrement={handleIncrement}
            onDecrement={handleDecrement}
            onRemove={handleRemoveItem}
            onItemEdit={handleItemEdit}
            onPlaceOrder={handlePlaceOrder}
            isPlacingOrder={isPlacingOrder}
          />
        </DialogContent>
      </Dialog>

      {/* Customization Modal - Fixed Accessibility */}
      <Dialog open={uiState.showCustomization} onOpenChange={(open) => setUiState(prev => ({ ...prev, showCustomization: open }))}>
        <DialogContent className="max-w-lg w-[95vw] h-[85vh] flex flex-col p-0 rounded-2xl" aria-describedby="customization-description">
          <DialogHeader className="p-4 border-b bg-white rounded-t-2xl">
            <DialogTitle className="text-xl font-bold text-gray-900">Personnaliser l'article</DialogTitle>
            <p id="customization-description" className="sr-only">
              Personnalisez votre article avec des suppléments et des notes spéciales
            </p>
          </DialogHeader>
          <MobileCustomizationPanel
            selectedItemForAddons={uiState.selectedItemForAddons}
            categories={categories}
            selectedItemSizes={uiState.selectedItemSizes}
            selectedAddons={uiState.selectedAddons}
            itemNotes={uiState.itemNotes}
            getAddonKey={getAddonKey}
            getItemNoteKey={getItemNoteKey}
            onToggleAddon={handleToggleAddon}
            onUpdateNote={handleUpdateNote}
            onClose={() => setUiState(prev => ({ ...prev, showCustomization: false, selectedItemForAddons: null }))}
            onConfirm={handleCustomizationConfirm}
          />
        </DialogContent>
      </Dialog>

      {/* Table Selection Dialog */}
      <Dialog open={uiState.showTableSelection} onOpenChange={() => {}}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="text-center">Sélectionner une table</DialogTitle>
          </DialogHeader>
          <div className="grid grid-cols-2 gap-3 p-4">
            {tables?.map((table) => (
              <Button
                key={table.id}
                variant="outline"
                onClick={() => {
                  dispatch({ type: 'SET_TABLE', payload: { tableId: table.id } });
                  setUiState(prev => ({ ...prev, showTableSelection: false }));
                }}
                className="h-16 flex flex-col items-center justify-center hover:bg-primary/5"
              >
                <div className="text-lg font-bold text-gray-900">{table.name}</div>
                <div className="text-xs text-gray-500">{table.seats} places</div>
              </Button>
            ))}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

// MenuItemsGrid Component
interface MenuItemsGridProps {
  categories: any[];
  selectedCategory: string;
  searchQuery: string;
  selectedItemForAddons: string | null;
  selectedItemSizes: {[key: string]: string};
  lastAddedItem: string | null;
  onItemSelect: (itemId: string, size: string) => void;
  onAddItem: (item: MenuItem, size: string) => void;
}

const MenuItemsGrid: React.FC<MenuItemsGridProps> = ({
  categories,
  selectedCategory,
  searchQuery,
  selectedItemForAddons,
  selectedItemSizes,
  lastAddedItem,
  onItemSelect,
  onAddItem
}) => {
  // Filter items based on category and search
  const filteredItems = useMemo(() => {
    const currentCategory = categories?.find(cat => cat.id === selectedCategory);
    if (!currentCategory?.items) return [];

    let items = currentCategory.items;

    if (searchQuery.trim()) {
      items = items.filter((item: MenuItem) =>
        item.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    return items;
  }, [categories, selectedCategory, searchQuery]);

  if (filteredItems.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="text-gray-300 mb-3">
          <Search className="h-10 w-10 mx-auto" />
        </div>
        <p className="text-gray-500 text-sm font-medium">
          {searchQuery ? 'Aucun article trouvé' : 'Aucun article dans cette catégorie'}
        </p>
        {searchQuery && (
          <p className="text-xs text-gray-400 mt-1">
            Essayez un autre terme de recherche
          </p>
        )}
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {filteredItems.map((item: MenuItem) => {
        const selectedSize = selectedItemSizes[item.id];
        const isSelected = selectedSize && selectedItemForAddons === `${item.id}-${selectedSize}`;

        return (
          <MobileMenuItemCard
            key={item.id}
            item={item}
            isSelected={isSelected}
            selectedSize={selectedSize}
            lastAddedItem={lastAddedItem}
            onSelect={onItemSelect}
            onAddItem={onAddItem}
          />
        );
      })}
    </div>
  );
};

// MobileMenuItemCard Component - Completely Redesigned
interface MobileMenuItemCardProps {
  item: MenuItem;
  isSelected: boolean;
  selectedSize: string | undefined;
  lastAddedItem: string | null;
  onSelect: (itemId: string, size: string) => void;
  onAddItem: (item: MenuItem, size: string) => void;
}

const MobileMenuItemCard: React.FC<MobileMenuItemCardProps> = ({
  item,
  isSelected,
  selectedSize,
  lastAddedItem,
  onSelect,
  onAddItem
}) => {
  const handleSizeClick = useCallback((size: string) => {
    onAddItem(item, size);
  }, [item, onAddItem]);

  const hasMultipleSizes = Object.keys(item.prices).length > 1;
  const sizes = Object.entries(item.prices);

  return (
    <div className="bg-white rounded-2xl border-2 border-gray-100 overflow-hidden shadow-sm hover:shadow-md hover:border-blue-200 transition-all duration-200 active:scale-[0.98]">
      {/* Compact Item Header */}
      <div className="p-3">
        <div className="flex items-start justify-between mb-2">
          <h3 className="font-bold text-base text-gray-900 leading-tight flex-1 pr-2">{item.name}</h3>
          {item.color && (
            <div
              className="w-3 h-3 rounded-full flex-shrink-0 mt-1"
              style={{ backgroundColor: item.color }}
            />
          )}
        </div>

        {/* Compact Size Buttons - Mobile Optimized */}
        {hasMultipleSizes ? (
          <div className="grid grid-cols-2 gap-2">
            {sizes.map(([size, price]) => {
              const isJustAdded = lastAddedItem === `${item.id}-${size}`;

              return (
                <Button
                  key={`${item.id}-${size}`}
                  variant="outline"
                  onClick={() => handleSizeClick(size)}
                  className={cn(
                    "h-12 flex flex-col items-center justify-center p-2 transition-all rounded-xl text-xs",
                    "border-2 hover:border-blue-400 hover:bg-blue-50 active:scale-95",
                    isJustAdded && "border-green-500 bg-green-50 text-green-700 animate-pulse"
                  )}
                >
                  <span className="font-semibold text-xs leading-none">
                    {size === "default" ? "Classique" : size}
                  </span>
                  <span className="font-bold text-blue-600 text-sm leading-none mt-1">
                    {price as number} DA
                  </span>
                </Button>
              );
            })}
          </div>
        ) : (
          <Button
            variant="outline"
            onClick={() => handleSizeClick(sizes[0][0])}
            className={cn(
              "w-full h-12 flex items-center justify-between px-3 transition-all rounded-xl",
              "border-2 hover:border-blue-400 hover:bg-blue-50 active:scale-95",
              lastAddedItem === `${item.id}-${sizes[0][0]}` && "border-green-500 bg-green-50 text-green-700 animate-pulse"
            )}
          >
            <span className="font-semibold text-sm">
              {sizes[0][0] === "default" ? "Classique" : sizes[0][0]}
            </span>
            <span className="font-bold text-blue-600 text-base">
              {sizes[0][1] as number} DA
            </span>
          </Button>
        )}
      </div>
    </div>
  );
};

// MobileOrderSummary Component
interface MobileOrderSummaryProps {
  items: OrderItem[];
  total: number;
  onIncrement: (itemId: string) => void;
  onDecrement: (itemId: string) => void;
  onRemove: (itemId: string) => void;
  onItemEdit: (item: OrderItem) => void;
  onPlaceOrder: () => void;
  isPlacingOrder: boolean;
}

const MobileOrderSummary: React.FC<MobileOrderSummaryProps> = ({
  items,
  total,
  onIncrement,
  onDecrement,
  onRemove,
  onItemEdit,
  onPlaceOrder,
  isPlacingOrder
}) => {
  const totalItemCount = useMemo(() => {
    return items.reduce((sum, item) => sum + item.quantity, 0);
  }, [items]);

  return (
    <div className="h-full flex flex-col">
      {/* Items List - Compact Mobile Design */}
      <div className="flex-1 overflow-hidden">
        {items.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full p-6 text-center">
            <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mb-3">
              <ShoppingCart className="h-6 w-6 text-gray-400" />
            </div>
            <p className="text-gray-500 text-sm">Aucun article</p>
          </div>
        ) : (
          <ScrollArea className="h-full">
            <div className="p-4 space-y-3">
              {items.map((item) => (
                <div key={item.id} className="bg-white rounded-xl p-4 border border-gray-200 shadow-sm">
                  <div className="flex justify-between items-start mb-3">
                    <div className="flex-1">
                      <h4 className="font-semibold text-base text-gray-900 leading-tight">{item.name}</h4>
                      <div className="flex items-center gap-2 mt-2">
                        <Badge variant="outline" className="text-xs px-2 py-1">
                          {item.size === 'default' ? 'Classique' : item.size}
                        </Badge>
                        <span className="text-sm text-gray-600 font-medium">
                          {item.price} DA
                        </span>
                      </div>
                      {item.addons.length > 0 && (
                        <p className="text-sm text-gray-600 mt-2 bg-gray-50 px-2 py-1 rounded">
                          + {item.addons.map(addon => addon.name).join(', ')}
                        </p>
                      )}
                      {item.notes && (
                        <p className="text-sm text-gray-600 mt-2 italic bg-blue-50 px-2 py-1 rounded">
                          Note: {item.notes}
                        </p>
                      )}
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onRemove(item.id)}
                      className="text-red-500 hover:text-red-700 hover:bg-red-50 p-2 h-8 w-8 rounded-full"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onDecrement(item.id)}
                        className="h-9 w-9 p-0 rounded-full"
                      >
                        <Minus className="h-4 w-4" />
                      </Button>
                      <span className="font-bold text-lg min-w-[2rem] text-center">
                        {item.quantity}
                      </span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onIncrement(item.id)}
                        className="h-9 w-9 p-0 rounded-full"
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-lg text-primary">
                        {((item.price + item.addons.reduce((sum, addon) => sum + addon.price, 0)) * item.quantity).toFixed(0)} DA
                      </p>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onItemEdit(item)}
                        className="text-xs text-blue-600 hover:text-blue-800 p-0 h-auto mt-1"
                      >
                        Modifier
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        )}
      </div>

      {/* Footer with Total and Place Order */}
      {items.length > 0 && (
        <div className="flex-shrink-0 p-3 border-t border-gray-200 bg-white space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-lg font-semibold text-gray-900">Total</span>
            <span className="text-xl font-bold text-primary">{total.toFixed(0)} DA</span>
          </div>
          <Button
            onClick={onPlaceOrder}
            disabled={isPlacingOrder}
            className="w-full h-11 text-base font-semibold"
          >
            {isPlacingOrder ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                Création...
              </>
            ) : (
              <>
                <Check className="h-4 w-4 mr-2" />
                Passer la commande
              </>
            )}
          </Button>
        </div>
      )}
    </div>
  );
};

// MobileCustomizationPanel Component
interface MobileCustomizationPanelProps {
  selectedItemForAddons: string;
  selectedItemSizes: {[key: string]: string};
  selectedAddons: {[key: string]: Set<string>};
  itemNotes: {[key: string]: string};
  categories: any[];
  getAddonKey: (itemId: string, size: string) => string;
  getItemNoteKey: (itemId: string, size: string) => string;
  onClose: () => void;
  onToggleAddon: (itemId: string, size: string, addonId: string) => void;
  onUpdateNote: (itemId: string, size: string, note: string) => void;
  onConfirm: () => void;
}

const MobileCustomizationPanel: React.FC<MobileCustomizationPanelProps> = ({
  selectedItemForAddons,
  selectedItemSizes,
  selectedAddons,
  itemNotes,
  categories,
  getAddonKey,
  getItemNoteKey,
  onClose,
  onToggleAddon,
  onUpdateNote,
  onConfirm
}) => {
  // Parse the selected item
  const [itemId, sizeFromKey] = selectedItemForAddons.split('-');
  const selectedSize = selectedItemSizes[itemId] || sizeFromKey || 'default';

  // Find the item and category
  let item: MenuItem | null = null;
  let category: any = null;

  for (const cat of categories || []) {
    const foundItem = cat.items?.find((menuItem: MenuItem) => menuItem.id === itemId);
    if (foundItem) {
      item = foundItem;
      category = cat;
      break;
    }
  }

  if (!item || !category) {
    return null;
  }

  const addonKey = getAddonKey(itemId, selectedSize);
  const noteKey = getItemNoteKey(itemId, selectedSize);
  const currentAddons = selectedAddons[addonKey] || new Set();
  const currentNote = itemNotes[noteKey] || '';

  return (
    <div className="fixed inset-0 bg-black/50 flex items-end z-50">
      <div className="w-full bg-white rounded-t-2xl max-h-[80vh] flex flex-col">
        {/* Header */}
        <div className="flex-shrink-0 p-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">{item.name}</h3>
              <p className="text-sm text-gray-500">
                {selectedSize === 'default' ? 'Classique' : selectedSize} - {item.prices[selectedSize]} DA
              </p>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden">
          <ScrollArea className="h-full">
            <div className="p-4 space-y-6">
              {/* Supplements Section */}
              <SupplementsSection
                categoryId={category.id}
                selectedSize={selectedSize}
                selectedAddons={currentAddons}
                onToggleAddon={(addonId: string) => onToggleAddon(itemId, selectedSize, addonId)}
              />

              {/* Pizza Builder Section (if applicable) */}
              {category.name.toLowerCase().includes('pizza') && (
                <PizzaBuilderSection
                  categoryId={category.id}
                  selectedSize={selectedSize}
                  categories={categories}
                />
              )}

              {/* Notes Section */}
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <StickyNote className="h-4 w-4 text-gray-500" />
                  <h4 className="font-medium text-gray-900">Notes spéciales</h4>
                </div>
                <Input
                  placeholder="Ajouter une note (ex: sans oignons, bien cuit...)"
                  value={currentNote}
                  onChange={(e) => onUpdateNote(itemId, selectedSize, e.target.value)}
                  className="w-full"
                />
              </div>
            </div>
          </ScrollArea>
        </div>

        {/* Footer */}
        <div className="flex-shrink-0 p-4 border-t border-gray-200">
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={onClose}
              className="flex-1"
            >
              Annuler
            </Button>
            <Button
              onClick={onConfirm}
              className="flex-1"
            >
              <Check className="h-4 w-4 mr-2" />
              Confirmer
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

// SupplementsSection Component
interface SupplementsSectionProps {
  categoryId: string;
  selectedSize: string;
  selectedAddons: Set<string>;
  onToggleAddon: (addonId: string) => void;
}

const SupplementsSection: React.FC<SupplementsSectionProps> = ({
  categoryId,
  selectedSize,
  selectedAddons,
  onToggleAddon
}) => {
  const { supplements, isLoading } = useSupplements(categoryId);

  const activeSupplements = supplements.filter(supplement => supplement.isActive !== false);

  if (isLoading) {
    return (
      <div className="space-y-3">
        <h4 className="font-medium text-gray-900">Suppléments</h4>
        <div className="space-y-2">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="h-12 bg-gray-200 rounded-lg animate-pulse" />
          ))}
        </div>
      </div>
    );
  }

  if (activeSupplements.length === 0) {
    return null;
  }

  return (
    <div className="space-y-3">
      <h4 className="font-medium text-gray-900">Suppléments</h4>
      <div className="space-y-2">
        {activeSupplements.map((supplement) => {
          const price = supplement.prices[selectedSize] || Object.values(supplement.prices)[0] || 0;
          const isSelected = selectedAddons.has(supplement.id);

          return (
            <div
              key={supplement.id}
              onClick={() => onToggleAddon(supplement.id)}
              className={cn(
                "flex items-center justify-between p-3 rounded-lg border cursor-pointer transition-all",
                isSelected
                  ? "border-primary bg-primary/5 text-primary"
                  : "border-gray-200 hover:border-gray-300"
              )}
            >
              <div className="flex items-center gap-3">
                <div className={cn(
                  "w-5 h-5 rounded border-2 flex items-center justify-center",
                  isSelected ? "border-primary bg-primary" : "border-gray-300"
                )}>
                  {isSelected && <Check className="h-3 w-3 text-white" />}
                </div>
                <span className="font-medium">{supplement.name}</span>
              </div>
              <span className="font-semibold">+{price} DA</span>
            </div>
          );
        })}
      </div>
    </div>
  );
};

// PizzaBuilderSection Component
interface PizzaBuilderSectionProps {
  categoryId: string;
  selectedSize: string;
  categories: any[];
}

const PizzaBuilderSection: React.FC<PizzaBuilderSectionProps> = ({
  categoryId,
  selectedSize,
  categories
}) => {
  const [quarters, setQuarters] = useState<(PizzaQuarter | null)[]>([null, null, null, null]);

  const currentCategory = categories?.find(cat => cat.id === categoryId);
  const pizzaItems = currentCategory?.items || [];

  const handleQuarterSelect = (quarterIndex: number, pizza: MenuItem) => {
    const price = pizza.prices[selectedSize] || Object.values(pizza.prices)[0] || 0;
    const newQuarters = [...quarters];
    newQuarters[quarterIndex] = {
      id: pizza.id,
      name: pizza.name,
      price: price
    };
    setQuarters(newQuarters);
  };

  const filledQuarters = quarters.filter(q => q !== null).length;

  if (pizzaItems.length === 0) {
    return null;
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <Pizza className="h-4 w-4 text-orange-500" />
        <h4 className="font-medium text-gray-900">Pizza Personnalisée</h4>
        <Badge variant="outline" className="text-xs">
          {filledQuarters}/4 quarts
        </Badge>
      </div>

      {/* Pizza Visual */}
      <div className="flex justify-center">
        <div className="relative w-32 h-32">
          <svg viewBox="0 0 100 100" className="w-full h-full">
            {/* Pizza base */}
            <circle cx="50" cy="50" r="48" fill="#f3f4f6" stroke="#d1d5db" strokeWidth="2"/>

            {/* Quarter lines */}
            <line x1="50" y1="2" x2="50" y2="98" stroke="#d1d5db" strokeWidth="1"/>
            <line x1="2" y1="50" x2="98" y2="50" stroke="#d1d5db" strokeWidth="1"/>

            {/* Quarter labels */}
            {quarters.map((quarter, index) => {
              const positions = [
                { x: 75, y: 25 }, // Top Right
                { x: 75, y: 75 }, // Bottom Right
                { x: 25, y: 75 }, // Bottom Left
                { x: 25, y: 25 }  // Top Left
              ];

              return (
                <g key={index}>
                  <circle
                    cx={positions[index].x}
                    cy={positions[index].y}
                    r="8"
                    fill={quarter ? "#10b981" : "#e5e7eb"}
                    className="cursor-pointer"
                  />
                  <text
                    x={positions[index].x}
                    y={positions[index].y + 1}
                    textAnchor="middle"
                    fontSize="6"
                    fill="white"
                    className="pointer-events-none font-bold"
                  >
                    {index + 1}
                  </text>
                </g>
              );
            })}
          </svg>
        </div>
      </div>

      {/* Quarter Selection */}
      <div className="grid grid-cols-2 gap-2">
        {quarters.map((quarter, index) => (
          <div key={index} className="space-y-2">
            <p className="text-sm font-medium text-gray-700">
              Quart {index + 1}
            </p>
            <select
              value={quarter?.id || ''}
              onChange={(e) => {
                const selectedPizza = pizzaItems.find((p: MenuItem) => p.id === e.target.value);
                if (selectedPizza) {
                  handleQuarterSelect(index, selectedPizza);
                }
              }}
              className="w-full p-2 border border-gray-300 rounded-lg text-sm"
            >
              <option value="">Choisir...</option>
              {pizzaItems.map((pizza: MenuItem) => (
                <option key={pizza.id} value={pizza.id}>
                  {pizza.name}
                </option>
              ))}
            </select>
          </div>
        ))}
      </div>
    </div>
  );
};

// Enhanced Mobile Customization Panel with Proper State Integration
interface MobileCustomizationPanelProps {
  selectedItemForAddons: string | null;
  categories: any[];
  selectedItemSizes: {[key: string]: string};
  selectedAddons: {[key: string]: Set<string>};
  itemNotes: {[key: string]: string};
  getAddonKey: (itemId: string, size: string) => string;
  getItemNoteKey: (itemId: string, size: string) => string;
  onToggleAddon: (itemId: string, size: string, addonId: string) => void;
  onUpdateNote: (itemId: string, size: string, note: string) => void;
  onClose: () => void;
  onConfirm: () => void;
}

const MobileCustomizationPanel: React.FC<MobileCustomizationPanelProps> = ({
  selectedItemForAddons,
  categories,
  selectedItemSizes,
  selectedAddons,
  itemNotes,
  getAddonKey,
  getItemNoteKey,
  onToggleAddon,
  onUpdateNote,
  onClose,
  onConfirm
}) => {

  if (!selectedItemForAddons) {
    return (
      <div className="flex-1 flex items-center justify-center p-6">
        <div className="text-center">
          <Package className="h-12 w-12 mx-auto text-gray-300 mb-3" />
          <p className="text-gray-500 text-sm">Aucun article sélectionné</p>
        </div>
      </div>
    );
  }

  const [itemId, sizeFromKey] = selectedItemForAddons.split('-');
  const selectedSize = selectedItemSizes[itemId] || sizeFromKey || 'default';

  // Find the item and category
  let item: MenuItem | null = null;
  let category: any = null;

  for (const cat of categories || []) {
    const foundItem = cat.items?.find((menuItem: MenuItem) => menuItem.id === itemId);
    if (foundItem) {
      item = foundItem;
      category = cat;
      break;
    }
  }

  if (!item || !category) {
    return (
      <div className="flex-1 flex items-center justify-center p-6">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 mx-auto text-red-300 mb-3" />
          <p className="text-gray-500 mb-3 text-sm">Article non trouvé</p>
          <Button onClick={onClose} variant="outline" size="sm">Fermer</Button>
        </div>
      </div>
    );
  }

  // Use the supplements hook properly
  const { supplements, isLoading: supplementsLoading, error: supplementsError, getAllSupplementPrices } = useSupplements(category.id);
  const activeSupplements = supplements?.filter(supplement => supplement.isActive !== false) || [];

  // Get supplement pricing from category config
  const [supplementPrices, setSupplementPrices] = useState<{[sizeName: string]: number}>({});

  useEffect(() => {
    const loadPrices = async () => {
      try {
        const prices = await getAllSupplementPrices();
        setSupplementPrices(prices);
      } catch (error) {
        console.error('Error loading supplement prices:', error);
      }
    };

    if (!supplementsLoading && category.id) {
      loadPrices();
    }
  }, [category.id, supplementsLoading, getAllSupplementPrices]);

  // Get current state for this item
  const addonKey = getAddonKey(itemId, selectedSize);
  const noteKey = getItemNoteKey(itemId, selectedSize);
  const currentSelectedAddons = selectedAddons[addonKey] || new Set();
  const currentNotes = itemNotes[noteKey] || '';

  // Check if it's a pizza category
  const isPizzaCategory = category.name.toLowerCase().includes('pizza');

  // Debug logging
  console.log('🔍 Mobile Customization Panel Debug:', {
    categoryId: category.id,
    categoryName: category.name,
    itemId,
    selectedSize,
    supplementsLoading,
    supplementsError: supplementsError?.message,
    supplementsCount: supplements?.length || 0,
    activeSupplementsCount: activeSupplements.length,
    supplementPrices,
    currentSelectedAddons: Array.from(currentSelectedAddons),
    currentNotes,
    isPizzaCategory,
    isQuarterable: category.isQuarterable
  });

  // Handle addon toggle
  const handleToggleAddon = (addonId: string) => {
    onToggleAddon(itemId, selectedSize, addonId);
  };

  // Handle notes update
  const handleNotesChange = (note: string) => {
    onUpdateNote(itemId, selectedSize, note);
  };

  return (
    <div className="flex-1 overflow-hidden flex flex-col">
      {/* Enhanced Mobile Header */}
      <div className="p-4 border-b bg-gradient-to-r from-blue-50 to-indigo-50">
        <div className="flex items-center justify-between">
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-lg text-gray-900 truncate">{item.name}</h3>
            <div className="flex items-center gap-2 mt-1">
              <Badge variant="secondary" className="text-xs">
                {selectedSize === 'default' ? 'Classique' : selectedSize}
              </Badge>
              {item.prices[selectedSize] && (
                <span className="text-sm font-medium text-blue-600">
                  {item.prices[selectedSize]} DA
                </span>
              )}
            </div>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose} className="h-9 w-9 p-0 ml-2">
            <X className="h-5 w-5" />
          </Button>
        </div>
      </div>

      <div className="flex-1 overflow-auto">
        <div className="p-4 space-y-5">
          {/* Enhanced Supplements Section */}
          {supplementsLoading ? (
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-gray-300 rounded animate-pulse" />
                <div className="w-20 h-4 bg-gray-300 rounded animate-pulse" />
              </div>
              {[...Array(3)].map((_, i) => (
                <div key={i} className="h-14 bg-gray-200 rounded-xl animate-pulse" />
              ))}
            </div>
          ) : activeSupplements.length > 0 ? (
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Package className="h-4 w-4 text-blue-500" />
                <h4 className="font-semibold text-base text-gray-900">Suppléments</h4>
                <Badge variant="outline" className="text-xs">
                  {activeSupplements.length} disponible{activeSupplements.length > 1 ? 's' : ''}
                </Badge>
              </div>
              <div className="space-y-2">
                {activeSupplements.map((supplement) => {
                  // Get price from category's supplement config, fallback to 0
                  const price = supplementPrices[selectedSize] || 0;
                  const isSelected = currentSelectedAddons.has(supplement.id);

                  return (
                    <div
                      key={supplement.id}
                      onClick={() => handleToggleAddon(supplement.id)}
                      className={cn(
                        "flex items-center justify-between p-4 rounded-xl border-2 cursor-pointer transition-all duration-200 active:scale-[0.98]",
                        isSelected
                          ? "border-blue-500 bg-blue-50 shadow-md"
                          : "border-gray-200 hover:border-blue-300 hover:bg-blue-50/30"
                      )}
                    >
                      <div className="flex items-center gap-3">
                        <div className={cn(
                          "w-6 h-6 rounded-full border-2 flex items-center justify-center transition-all",
                          isSelected ? "border-blue-500 bg-blue-500" : "border-gray-300"
                        )}>
                          {isSelected && <Check className="h-4 w-4 text-white" />}
                        </div>
                        <div className="flex flex-col">
                          <span className="font-semibold text-sm text-gray-900">{supplement.name}</span>
                          {supplement.description && (
                            <span className="text-xs text-gray-600 mt-0.5">{supplement.description}</span>
                          )}
                        </div>
                      </div>
                      <div className="text-right">
                        <span className="font-bold text-sm text-blue-600">+{price} DA</span>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          ) : (
            <div className="text-center py-6">
              <div className="text-gray-300 mb-3">
                <Package className="h-10 w-10 mx-auto" />
              </div>
              <p className="text-gray-500 text-sm font-medium">Aucun supplément disponible</p>
              <p className="text-xs text-gray-400 mt-1">pour la catégorie {category.name}</p>
              {/* Enhanced debug info in development */}
              {process.env.NODE_ENV === 'development' && (
                <div className="mt-4 p-3 bg-gray-100 rounded-lg text-xs text-left space-y-1">
                  <p><strong>Debug Info:</strong></p>
                  <p>• Category ID: {category.id}</p>
                  <p>• Category Name: {category.name}</p>
                  <p>• Supplements in category: {category.supplements?.length || 0}</p>
                  <p>• Supplement config: {category.supplementConfig ? 'Yes' : 'No'}</p>
                  <p>• Active supplements: {activeSupplements.length}</p>
                  <p>• Loading: {supplementsLoading ? 'Yes' : 'No'}</p>
                  <p>• Error: {supplementsError?.message || 'None'}</p>
                </div>
              )}
            </div>
          )}

          {/* Functional Pizza Builder Section */}
          {isPizzaCategory && category.isQuarterable && (
            <MobilePizzaBuilder
              category={category}
              selectedSize={selectedSize}
              onPizzaChange={(quarters) => {
                // Handle pizza quarter changes
                console.log('Pizza quarters changed:', quarters);
              }}
            />
          )}

          {/* Enhanced Notes Section */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <StickyNote className="h-5 w-5 text-amber-500" />
              <h4 className="font-semibold text-base text-gray-900">Notes spéciales</h4>
            </div>
            <div className="relative">
              <Input
                placeholder="Instructions spéciales pour la cuisine..."
                value={currentNotes}
                onChange={(e) => handleNotesChange(e.target.value)}
                className="w-full h-12 text-sm rounded-xl border-2 border-gray-200 focus:border-amber-400 pl-4 pr-10"
              />
              {currentNotes && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleNotesChange('')}
                  className="absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8 p-0 text-gray-400 hover:text-gray-600"
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Action Buttons */}
      <div className="p-4 border-t bg-white">
        <div className="flex gap-3">
          <Button
            variant="outline"
            onClick={onClose}
            className="flex-1 h-12 text-sm font-medium border-2 hover:bg-gray-50"
          >
            Annuler
          </Button>
          <Button
            onClick={onConfirm}
            className="flex-1 h-12 text-sm font-semibold bg-blue-600 hover:bg-blue-700"
          >
            <Check className="h-4 w-4 mr-2" />
            Confirmer
          </Button>
        </div>
      </div>
    </div>
  );
};

// Mobile Pizza Builder Component
interface MobilePizzaBuilderProps {
  category: any;
  selectedSize: string;
  onPizzaChange: (quarters: (PizzaQuarter | null)[]) => void;
}

const MobilePizzaBuilder: React.FC<MobilePizzaBuilderProps> = ({
  category,
  selectedSize,
  onPizzaChange
}) => {
  const [quarters, setQuarters] = useState<(PizzaQuarter | null)[]>([null, null, null, null]);
  const [selectedQuarter, setSelectedQuarter] = useState<number | null>(null);

  const pizzaItems = category.items || [];
  const filledQuarters = quarters.filter(q => q !== null).length;

  const handleQuarterSelect = (quarterIndex: number, pizza: MenuItem) => {
    const price = pizza.prices[selectedSize] || Object.values(pizza.prices)[0] || 0;
    const newQuarters = [...quarters];
    newQuarters[quarterIndex] = {
      id: pizza.id,
      name: pizza.name,
      price: price
    };
    setQuarters(newQuarters);
    onPizzaChange(newQuarters);
    setSelectedQuarter(null);
  };

  const clearQuarter = (quarterIndex: number) => {
    const newQuarters = [...quarters];
    newQuarters[quarterIndex] = null;
    setQuarters(newQuarters);
    onPizzaChange(newQuarters);
  };

  const getQuarterColor = (index: number) => {
    const quarter = quarters[index];
    if (!quarter) return '#f3f4f6';

    // Generate a color based on the pizza name
    const colors = ['#fef3c7', '#fce7f3', '#e0f2fe', '#f0fdf4', '#fef7cd', '#f3e8ff'];
    const colorIndex = quarter.id.length % colors.length;
    return colors[colorIndex];
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <Pizza className="h-5 w-5 text-orange-500" />
        <h4 className="font-semibold text-base text-gray-900">Pizza Personnalisée</h4>
        <Badge variant="outline" className="text-xs bg-orange-50 text-orange-700 border-orange-200">
          {filledQuarters}/4 quarts
        </Badge>
      </div>

      {/* Interactive Pizza Visual */}
      <div className="p-4 bg-gradient-to-br from-orange-50 to-yellow-50 rounded-xl border-2 border-orange-200">
        <div className="flex items-center justify-center mb-4">
          <div className="w-32 h-32 relative">
            <svg viewBox="0 0 100 100" className="w-full h-full drop-shadow-sm">
              {/* Pizza base */}
              <circle cx="50" cy="50" r="45" fill="#fef3c7" stroke="#f59e0b" strokeWidth="2"/>

              {/* Quarter segments */}
              <path
                d="M 50 5 A 45 45 0 0 1 95 50 L 50 50 Z"
                fill={getQuarterColor(0)}
                stroke="#f59e0b"
                strokeWidth="1"
                className="cursor-pointer hover:opacity-80 transition-opacity"
                onClick={() => setSelectedQuarter(selectedQuarter === 0 ? null : 0)}
              />
              <path
                d="M 95 50 A 45 45 0 0 1 50 95 L 50 50 Z"
                fill={getQuarterColor(1)}
                stroke="#f59e0b"
                strokeWidth="1"
                className="cursor-pointer hover:opacity-80 transition-opacity"
                onClick={() => setSelectedQuarter(selectedQuarter === 1 ? null : 1)}
              />
              <path
                d="M 50 95 A 45 45 0 0 1 5 50 L 50 50 Z"
                fill={getQuarterColor(2)}
                stroke="#f59e0b"
                strokeWidth="1"
                className="cursor-pointer hover:opacity-80 transition-opacity"
                onClick={() => setSelectedQuarter(selectedQuarter === 2 ? null : 2)}
              />
              <path
                d="M 5 50 A 45 45 0 0 1 50 5 L 50 50 Z"
                fill={getQuarterColor(3)}
                stroke="#f59e0b"
                strokeWidth="1"
                className="cursor-pointer hover:opacity-80 transition-opacity"
                onClick={() => setSelectedQuarter(selectedQuarter === 3 ? null : 3)}
              />

              {/* Quarter labels */}
              <text x="72" y="30" fontSize="8" textAnchor="middle" fill="#92400e" className="pointer-events-none font-bold">1</text>
              <text x="72" y="75" fontSize="8" textAnchor="middle" fill="#92400e" className="pointer-events-none font-bold">2</text>
              <text x="28" y="75" fontSize="8" textAnchor="middle" fill="#92400e" className="pointer-events-none font-bold">3</text>
              <text x="28" y="30" fontSize="8" textAnchor="middle" fill="#92400e" className="pointer-events-none font-bold">4</text>
            </svg>
          </div>
        </div>

        {/* Quarter Selection Interface */}
        {selectedQuarter !== null && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h5 className="font-semibold text-sm text-orange-800">
                Quart {selectedQuarter + 1}
              </h5>
              {quarters[selectedQuarter] && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => clearQuarter(selectedQuarter)}
                  className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                >
                  <X className="h-3 w-3" />
                </Button>
              )}
            </div>

            <div className="grid grid-cols-1 gap-2 max-h-32 overflow-y-auto">
              {pizzaItems.map((pizza: MenuItem) => {
                const price = pizza.prices[selectedSize] || Object.values(pizza.prices)[0] || 0;
                const isSelected = quarters[selectedQuarter]?.id === pizza.id;

                return (
                  <Button
                    key={pizza.id}
                    variant={isSelected ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleQuarterSelect(selectedQuarter, pizza)}
                    className={cn(
                      "justify-between h-8 text-xs",
                      isSelected && "bg-orange-600 text-white"
                    )}
                  >
                    <span className="truncate">{pizza.name}</span>
                    <span className="font-bold">{price} DA</span>
                  </Button>
                );
              })}
            </div>
          </div>
        )}

        {/* Quarter Summary */}
        {filledQuarters > 0 && (
          <div className="mt-4 pt-3 border-t border-orange-200">
            <div className="space-y-1">
              {quarters.map((quarter, index) => (
                quarter && (
                  <div key={index} className="flex items-center justify-between text-xs">
                    <span className="text-orange-700">Quart {index + 1}: {quarter.name}</span>
                    <span className="font-semibold text-orange-800">{quarter.price} DA</span>
                  </div>
                )
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MobileOrderingInterface;
